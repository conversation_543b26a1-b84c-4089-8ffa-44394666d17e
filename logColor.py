import logging
from ColorInfo import ColorLogger


class LogColor:

    logging.basicConfig(level=logging.DEBUG,
                        # 设置输出格式
                        format='%(asctime)s - %(name)s - %(levelname)-8s: %(message)s',
                        datefmt='%a, %d %b %Y %H:%M:%S',
                        filename='log.log',
                        filemode='w')   # 输出到文件


    @staticmethod
    def info(message: str):
        # info级别的日志，绿色
        logging.info("\033[0;32m" + message + "\033[0m")

    @staticmethod
    def warning(message: str):
        # warning级别的日志，黄色
        logging.warning("\033[0;33m" + message + "\033[0m")

    @staticmethod
    def error(message: str):
        # error级别的日志，红色
        logging.error("\033[0;31m"+"-" * 23 + '\n| ' + message + "\033[0m" + "\n" + "└"+"-" * 55)

    @staticmethod
    def debug(message: str):
        # debug级别的日志，灰色
        logging.debug("\033[0;37m" + message + "\033[0m")



if __name__ == '__main__':
    logger = ColorLogger()
    logger.info("info")  # 绿色
    logger.warning("warning")   # 黄色
    logger.error("error")                   
    logger. debug("测试颜色")  
