import requests

url = "http://10.254.99.107/gateway/hi-api/v2/event-analysis-list"
headers = {
    "Content-Type": "application/json",
    "hi-api-key": "#K-mLamkc3UtsBvjLm0EPONk9v3AupHh2y3S"
}
data = {
    "projectCode": "wechatTest",
    "bookmarkId": 1914940786739638273,
    "page": 1,
    "pageSize": 1000
}

response = requests.post(url, headers=headers, json=data)

result = response.json()

 # 提取并打印关键字段
status = result.get("status")
code = result.get("code")
message = result.get("message")
success = result.get("success")
data = result.get("data")

print("数据",result)

if data:
        total = data.get("total")
        page = data.get("page")
        result_list = data.get("result")
        
        print(f"数据总条数: {total}")
        print(f"页码: {page}")
        
        for item in result_list:
            analysis_name = item.get("analysis_name")
            pk_day = item.get("pk_day")
            calres = item.get("calres")
            event_time = item.get("event.event_time")
            timezone_offset = item.get("event.timezone_offset")
            
            print(f"分析指标名称: {analysis_name}")
            print(f"时间: {pk_day}")
            print(f"计算结果: {calres}")
            print(f"事件时间: {event_time}")
            print(f"时区偏移: {timezone_offset}")
            print("-" * 30)
else:
    print(f"请求失败，状态码: {response.status_code}")