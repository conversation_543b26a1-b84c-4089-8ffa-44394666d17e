from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes

private_key_pem="MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMDkAzAnEl4rLlP+5/oMV5cExtuSCzyhNtlhN2ovC5x7wtzsAEQZmBSaXa652kAk5ba/Oks9zPi9aEsR9PcWwlshwSWTXswuh5+J8Vd48G67XTWdGFWjaRx6e0Y/jfSdw2e6f69LmgAo6/Gr3Y1FdP5p4igiQSDZe0BZ4iorqAqZAgMBAAECgYAAvqCYhf4XKPmDz38bwwJvjdAqttSeRk0M58gr+8SCtSOacLrLiIHCypnD++mwx7OvUeuqsLFi4HBPoeEdNxRG/hIZe2+ms70/L09YZmrAR0qCAwfMQlhkeovu4uGLj79G51kLSQIGovuCKQMMmU/UIYtUk8Yx6KB/ULkTLGn+lQJBAOFXU0oTZIflmFCNZMy/4NyAkD9ae0Edny+Nv7GdGWzdt2sEc/fQva0wFTHX+NEBpgSXjUNT+a2Jvx5UaNjJeRsCQQDbIm6VDIeiox3u/Qgpgc+ZQomAVlm7sNpx+TraZ87AvpRbrS8h4+16kz4ZrvQ3Tnhl1h5OdhMdXdT+coowS9pbAkBwD2w1B0XUKwI+9MGu7LDXFvwk9UscC64RCO3OVvDA6dV/27wL/fuFd8bifaOX1LkJyZAPbmBYw4qOe62UOUUHAkBLUB4pY9EJ+H3FMXmoqlCrH88aKJNWioXJPhsYDS1l2RosF+1m/GsWAZ0KPrL4fyOp/FkWJkaThTg66yrLRoaZAkEA3Vs6IEEirQzWFcLikLMaQZ5jMHi2Do9IAZFOlnkDPm+5EJ3kTb4ag1xR1fC/uvz8X+Z8bTa0ZSaSHHV+LsO5Uw=="


# 假设你已经有了私钥和公钥
private_key = serialization.load_pem_private_key(
    private_key_pem,
    password=None,
    backend=default_backend()
)


public_key = private_key.public_key()

# 私钥加密
message = b"123"
encrypted = private_key.encrypt(
    message,
    padding.OAEP(
        mgf=padding.MGF1(algorithm=hashes.SHA256()),
        algorithm=hashes.SHA256(),
        label=None
    )
)

# 公钥解密
""" decrypted = public_key.decrypt(
    encrypted,
    padding.OAEP(
        mgf=padding.MGF1(algorithm=hashes.SHA256()),
        algorithm=hashes.SHA256(),
        label=None
    )
)
print(decrypted)   """

if __name__ == '__main__':
    
    print(encrypted)
