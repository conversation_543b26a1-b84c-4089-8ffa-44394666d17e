import requests

if __name__ == '__main__':
        # API URL
    url = "http://10.254.99.107/gateway/hi-api/v1/data-export"

    # 请求头
    headers = {
        "Content-Type": "application/json",
        "hi-api-key": "#K-mLamkc3UtsBvjLm0EPONk9v3AupHh2y3S"
    }

    # 请求体
    data = {
        "sql": "select * from dwd_event_abbcc_cc",
        "beginDate": "2025-05-11",
        "endDate": "2025-05-17"
    }

    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=data)

    # 检查响应状态
    if response.status_code == 200:
        result = response.json()
        print("请求结果:", result)
        if result["success"]:
            download_url = result["data"]
            print("导出文件的下载地址:", download_url)
        else:
            print("请求失败:", result["message"])
    else:
        print("请求失败:", response.status_code)

