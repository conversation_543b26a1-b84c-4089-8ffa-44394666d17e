def read_ids_from_file(file_path):
    with open(file_path, 'r', encoding='utf-8') as file:
        lines = file.readlines()
        # Remove any lines that don't look like IDs (e.g. header)
        ids = [line.strip() for line in lines if line.strip() and not line.startswith(('+', '-', '=', '@'))]
    return ids

file1_ids = read_ids_from_file('D:/Python path/exam-test/tmp/数据1.txt')
file2_ids = read_ids_from_file('D:/Python path/exam-test/tmp/数据2.txt')


unique_to_file1 = set(file1_ids) - set(file2_ids)
unique_to_file2 = set(file2_ids) - set(file1_ids)

print("Unique to 数据1.txt:", unique_to_file1)
print("Unique to 数据2.txt:", unique_to_file2)