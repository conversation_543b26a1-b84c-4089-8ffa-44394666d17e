
import base64

if __name__ == '__main__':
    
    base64_str = 'eyJwcm9wZXJ0aWVzIjp7Ikhfb3MiOiJXaW5kb3dzIiwiSF9vc192ZXJzaW9uIjoiMTAuMCIsIkhfbGliX3ZlcnNpb24iOiIxLjAuMCIsIkhfbGliIjoianMiLCJIX2xpYl9tZXRob2QiOiJjb2RlIiwiSF9zY3JlZW5faGVpZ2h0Ijo4NjQsIkhfc2NyZWVuX3dpZHRoIjoxNTM2LCJIX2Jyb3dzZXIiOiJlZGdlIiwiSF9icm93c2VyX3ZlcnNpb24iOiIxMzEuMC4wLjAiLCJIX25ldHdvcmtfdHlwZSI6IjRnIiwiSF9sYW5ndWFnZSI6InpoLWNuIiwiSF9tb2RlbCI6IldpbmRvd3MiLCJIX3VybCI6Imh0dHBzOi8vd3d3LmV2ZW50d2FuZy5jbi9EZXNpZ25SZXNvdXJjZS9kZXNpZ25SZXNvdXJjZUxpc3Q/dHlwZT00JnBhZ2U9NSZzZWFyY2hlZD0xJmtleXdvcmRzPSVFNiU5OSU5QSVFNCVCQyU5QSZvcmRlcmJ5PXNvcnRfYnV5X2NsaWNrX3JhdGUiLCJIX3RpdGxlIjoi562W5YiS5pa55qGIX etluWIkuaWueahiOaooeadvy/moYjkvosv6K6 6K6hL aAjuS5iOWGmSIsIkhfbG9uZ190YXNrX3RpbWUiOjE1MCwiSF9sb25nX3Rhc2siOjEsIkhfc2Vzc2lvbl9pZCI6IjE3MzYyMTI3NzgzOThfNzMxNDY5In0sInR5cGUiOiJ0cmFjayIsImV2ZW50IjoiSF9wZXJmb3JtYW5jZV9wYWdlIiwidGltZSI6MTczNjIxMzI2MjU5MiwiX3RyYWNrX2lkIjozNjIzNjI1OTMsImFub255bW91c19pZCI6IjE5M2E5NmQ4YTcwNGI1LTAzZGE3ZDgzMWZlZjg0LTRjNjU3YjU4LTEzMjcxMDQtMTkzYTk2ZDhhNzExOTk4IiwiYWNjb3VudF9pZCI6IjQ4NzY0NCIsInNlbmRfdGltZSI6MTczNjIxMzI2MjU5M30='

    decoded_bytes = base64.b64decode(base64_str)
    result = decoded_bytes.decode('utf-8')

    print(result)

