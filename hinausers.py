from datetime import datetime, timedelta
import csv
import os
import requests
import pandas as pd

# 设置请求的 headers
headers = {
    'Haina-Auth': 'bearer 966d81ed-3a35-44ea-bee6-79bc6fee2193',  
    'User-Agent': 'Apifox/1.0.0 (https://apifox.com)',
    'Content-Type': 'application/json',
    'Project-Code': 'hinacloudAll'
}
directory = 'tmp'
def create_directory(directory_name):
        current_directory = os.path.dirname(os.path.abspath(__file__))
        new_directory_path = os.path.join(current_directory, directory_name)
        if not os.path.exists(new_directory_path):
            os.makedirs(new_directory_path)
            print("文件夹已创建")
        else:
            print("文件夹已存在")
        return new_directory_path


def no_data():
    # 设置请求的 URL
    url = 'https://hicloud.haishuu.com/gateway/hina-cloud-poc/tenant/management/list'

    # 设置请求的数据
    data = {}

    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=data)

    new_directory_name = create_directory(directory)

    # 检查请求是否成功
    if response.status_code == 200:
        # 解析返回的 JSON 数据
        json_data = response.json()
        current_date = datetime.now()
        forty_four_days_ago = current_date - timedelta(days=44)

        # 获取 data 数组
        tenants_data = json_data.get('data', [])

        # 过滤掉 status 为 FREEZE 或 AUDIT_REJECT 的项目
        # 以及 tenantId 以 h00000 或 h100002 开头的项目
        tenants = [
            tenant for tenant in tenants_data
            if tenant['status'] not in ['FREEZE', 'AUDIT_REJECT']
               and tenant['tenantId'] not in ['h000000', 'h898119']
               and datetime.strptime(tenant['createTime'], "%Y-%m-%d %H:%M:%S") < forty_four_days_ago
        ]

        # 拼接 SQL 语句
        if tenants:
            tenant_ids = [f"'{tenant['tenantId']}'" for tenant in tenants]
            sql = f"""
                    SELECT tenant_id AS "租户ID", tenant_name AS "租户名称", 
                           SUM(event_count) AS "三个月+两周内总事件数", 
                           SUM(daily_new_users) AS "三个月+两周内总新增用户数", 
                           MAX(daily_new_users) AS "三个月+两周内最大活跃用户数" 
                    FROM ads_tenant_data_hinacloudAll 
                    WHERE part_date >= date_sub(curdate(), INTERVAL 104 DAY) 
                    AND tenant_id IN ({', '.join(tenant_ids)})
                    GROUP BY tenant_id, tenant_name 
                    HAVING SUM(event_count) = 0 AND SUM(daily_new_users) = 0 
                    ORDER BY tenant_id ASC
                    """

            # 准备请求体 JSON 数据
            payload = {
                "stmt": sql,
                "ifSaveRecord": "true"
            }

            # 设置另一个接口的 URL
            another_url = 'https://hicloud.haishuu.com/gateway/hina-cloud-service/analysis/custom/queryAll'  # 请替换为实际的接口 URL

            # 发送 POST 请求到另一个接口
            another_response = requests.post(another_url, headers=headers, json=payload)

            # 检查请求是否成功
            if another_response.status_code == 200:
                # 解析响应
                another_json_data = another_response.json()
                print(another_json_data)

                # 获取表体
                rows = another_json_data.get('data', {})[0].get('data', [])

                column_names = ["租户ID", "租户名称", "一个月+两周内总事件数", "一个月+两周内总新增用户数",
                                "一个月+两周内最大活跃用户数", "客户联系人", "创建时间"]

                # 更新 rows 数据
                for row in rows:
                    for tenant in tenants:
                        if row[0] == tenant['tenantId']:
                            if 'createUserName' in tenant:
                                row.append(tenant['createUserName'])
                            else:
                                row.append('')
                            row.append(tenant['createTime'])
                            break
                # 将匹配的数据输出为 CSV 格式
                csv_file_path = os.path.join(new_directory_name, '无数据租户信息.csv')
                with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(column_names)
                    for row in rows:
                        writer.writerow(row)


            else:
                print(f"请求失败，状态码：{another_response.status_code}")
        else:
            print("没有符合条件的 tenants")
    else:
        print(f"请求失败，状态码：{response.status_code}")


def data():
    # 设置请求的 URL
    url = 'https://hicloud.haishuu.com/gateway/hina-cloud-poc/tenant/management/list'

    # 设置请求的数据
    data = {}

    new_directory_name = create_directory(directory)
    # 发送 POST 请求
    response = requests.post(url, headers=headers, json=data)

    # 检查请求是否成功
    if response.status_code == 200:
        # 解析返回的 JSON 数据
        json_data = response.json()
        current_date = datetime.now()
        forty_four_days_ago = current_date - timedelta(days=44)

        # 获取 data 数组
        tenants_data = json_data.get('data', [])

        # 过滤掉 status 为 FREEZE 或 AUDIT_REJECT 的项目
        # 以及 tenantId 为 'h000000', 'h898119'的项目
        tenants = [
            tenant for tenant in tenants_data
            if tenant['status'] not in ['FREEZE', 'AUDIT_REJECT']
               and tenant['tenantId'] not in ['h000000', 'h898119', 'h864806', 'h867044']
               and datetime.strptime(tenant['createTime'], "%Y-%m-%d %H:%M:%S") < forty_four_days_ago
        ]

        valid_tenants = []

        contract_url = 'https://hicloud.haishuu.com/gateway/hina-cloud-poc/contract/list'

        # 遍历 tenants 数组
        for tenant in tenants:
            contract_data = {
                "tenantId": tenant['tenantId']
            }

            # 发送 POST 请求到合同列表接口
            response = requests.post(contract_url, headers=headers, json=contract_data)

            # 检查请求是否成功
            if response.status_code == 200:
                # 解析返回的 JSON 数据
                contract_json_data = response.json()

                # 检查 data 是否为空或者是否有 status 不等于 VALID 的项
                if not contract_json_data.get('data') or any(item['status'] != 'VALID' for item in contract_json_data.get('data', [])):
                    valid_tenants.append(tenant)
                else:
                    print(f"删除当前租户信息：{contract_json_data}")
            else:
                print(f"请求失败，状态码：{response.status_code}")

        # 拼接 SQL 语句
        if valid_tenants:
            tenant_ids = [f"'{tenant['tenantId']}'" for tenant in valid_tenants]
            sql = f"""
                    SELECT tenant_id AS "租户ID", tenant_name AS "租户名称", 
                           SUM(event_count) AS "三个月+两周内总事件数", 
                           SUM(daily_new_users) AS "三个月+两周内总新增用户数", 
                           MAX(daily_new_users) AS "三个月+两周内最大活跃用户数" 
                    FROM ads_tenant_data_hinacloudAll
                    WHERE part_date >= date_sub(curdate(), INTERVAL 104 DAY) 
                    AND tenant_id IN ({', '.join(tenant_ids)})
                    GROUP BY tenant_id, tenant_name 
                    HAVING SUM(event_count) <> 0 AND SUM(daily_new_users) <> 0 
                    ORDER BY tenant_id ASC
                    """

            # 准备请求体 JSON 数据
            payload = {
                "stmt": sql,
                "ifSaveRecord": "true"
            }
            # 设置另一个接口的 URL
            another_url = 'https://hicloud.haishuu.com/gateway/hina-cloud-service/analysis/custom/queryAll'  # 请替换为实际的接口 URL

            # 发送 POST 请求到另一个接口
            another_response = requests.post(another_url, headers=headers, json=payload)

            # 检查请求是否成功
            if another_response.status_code == 200:
                # 解析响应
                another_json_data = another_response.json()

                # 获取表体
                rows = another_json_data.get('data', {})[0].get('data', [])

                column_names = ["租户ID", "租户名称", "三个月+两周内总事件数", "三个月+两周内总新增用户数",
                                "三个月+两周内最大活跃用户数", "客户联系人", "创建时间"]

                # 更新 rows 数据
                for row in rows:
                    for tenant in tenants:
                        if row[0] == tenant['tenantId']:
                            if 'createUserName' in tenant:
                                row.append(tenant['createUserName'])
                            else:
                                row.append('')
                            row.append(tenant['createTime'])
                            break
                # 将匹配的数据输出为 CSV 格式
                csv_file_path = os.path.join(new_directory_name, '有数据租户信息.csv')
                with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerow(column_names)
                    for row in rows:
                        writer.writerow(row)



            else:
                print(f"请求失败，状态码：{another_response.status_code}")
        else:
            print("没有符合条件的 tenants")
    else:
        print(f"请求失败，状态码：{response.status_code}")


if __name__ == '__main__':
    no_data()
    data()

    # 读取 CSV 文件
    csv_filename = r'tmp\有数据租户信息.csv'
    df = pd.read_csv(csv_filename, encoding='utf-8-sig')

    # 将 DataFrame 导出到 Excel 文件
    excel_filename = r'tmp\有数据租户信息.xlsx'
    df.to_excel(excel_filename, index=False)

    # 读取 CSV 文件
    csv_filename = r'tmp\无数据租户信息.csv'
    df = pd.read_csv(csv_filename, encoding='utf-8-sig')

    # 将 DataFrame 导出到 Excel 文件
    excel_filename = r'tmp\无数据租户信息.xlsx'
    df.to_excel(excel_filename, index=False)

    print(f"CSV 数据已成功转换为 Excel 文件：{excel_filename}")
