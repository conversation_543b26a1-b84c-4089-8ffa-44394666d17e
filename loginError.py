# Generated by Selenium IDE
import pytest
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.support import expected_conditions
from selenium.webdriver.support.wait import Web<PERSON><PERSON><PERSON>ait
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities
import csv


class TestLoginError():
  def setup_method(self, method):
    self.driver = webdriver.Edge()
    self.vars = {}

  def teardown_method(self, method):
    self.driver.quit()

  def test_invalidLogin(self):
    self.driver.get("http://localhost:7272/")
    self.driver.set_window_size(1019, 744)

    file = open('D:/data/users.csv', mode='r', encoding='utf-8')
    readAll = csv.reader(file)
    for row in readAll:
      username = row[0].strip()
      password = row[1].strip()

      self.driver.find_element(By.ID, "username_field").clear()
      self.driver.find_element(By.ID, "username_field").send_keys(username)
      self.driver.find_element(By.ID, "password_field").clear()
      self.driver.find_element(By.ID, "password_field").send_keys(password)
      self.driver.find_element(By.ID, "login_button").click()
      elements = self.driver.find_elements(
          By.XPATH, "//h1[contains(.,\'Error Page\')]")
      if len(elements) > 0:
        print('error page')
      else:
        print('index page')

      time.sleep(1)  
      self.driver.back()
